import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { Base } from '../../../common/model/base.model';

@Entity('ai_assistant_user_base_info')
export class User extends Base {
  @Column({ name: 'user_id', unique: true, comment: '用户ID' })
  userId: string;

  @Column({ name: 'username', length: 100, comment: '用户名' })
  username: string;

  @Column({ name: 'password_hash', length: 255, comment: '密码哈希值' })
  passwordHash: string;

  @Column({ name: 'email', length: 100, nullable: true, comment: '邮箱' })
  email?: string;

  @Column({
    name: 'avatar_url',
    length: 500,
    nullable: true,
    comment: '头像URL',
  })
  avatarUrl?: string;

  // 关联关系
  @OneToOne('UserConfig', 'user')
  config: any;

  @OneToMany('UserSkill', 'user')
  skills: any[];
}
