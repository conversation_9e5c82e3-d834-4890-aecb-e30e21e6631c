import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON>o<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Base } from '../../../common/model/base.model';

@Entity('ai_assistant_user_global_config')
export class UserConfig extends Base {
  @Column({ name: 'user_id', comment: '外键，关联用户基本信息表' })
  userId: string;

  @Column({
    name: 'translate_source_language',
    length: 10,
    default: 'auto',
    comment: '翻译源语言',
  })
  sourceLanguage: string;

  @Column({
    name: 'translate_target_language',
    length: 10,
    default: 'zh-CN',
    comment: '翻译目标语言',
  })
  targetLanguage: string;

  @Column({
    name: 'floating_ball_expanded',
    type: 'boolean',
    default: false,
    comment: '悬浮球是否展开',
  })
  floatingBallExpanded: boolean;

  @Column({
    name: 'blocked_websites',
    type: 'json',
    nullable: true,
    comment: '禁用网站URL数组（JSON格式存储）',
  })
  blockedWebsites?: string[];

  // 关联关系
  @OneToOne('User', 'config')
  @JoinColumn({ name: 'user_id' })
  user: any;
}
